#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
元信息成图分析器
根据论文的元信息构建图结构，分析不同元信息类别标签之间的共现关系
"""

import json
import networkx as nx
from collections import defaultdict, Counter
from typing import Dict, List, Set, Tuple
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  # 支持中文显示
matplotlib.rcParams['axes.unicode_minus'] = False


class MetaInfoGraphBuilder:
    """元信息图构建器"""
    
    def __init__(self):
        """初始化图构建器"""
        self.graph = nx.Graph()  # 使用无向图
        self.node_info = {}  # 存储节点的详细信息
        self.edge_weights = defaultdict(int)  # 存储边的权重
        self.meta_type_labels = defaultdict(set)  # 按元信息类型分组的标签
        
    def load_data(self, file_path: str) -> List[Dict]:
        """
        加载JSON数据文件
        
        Args:
            file_path: JSON文件路径
            
        Returns:
            论文数据列表
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"成功加载 {len(data)} 篇论文的数据")
            return data
        except Exception as e:
            print(f"加载数据文件失败: {e}")
            return []
    
    def extract_labels_from_paper(self, paper: Dict) -> Dict[str, Set[str]]:
        """
        从单篇论文中提取所有元信息类别的标签
        
        Args:
            paper: 论文数据字典
            
        Returns:
            按元信息类型分组的标签字典
        """
        paper_labels = defaultdict(set)
        
        if 'cleaned_meta_info' not in paper:
            print("没找到 cleaned_meta_info")
            return paper_labels
            
        for meta_info in paper['cleaned_meta_info']:
            meta_type = meta_info.get('meta_info_type', '')
            parent_cls_dict = meta_info.get('parent_cls_dict', {})
            
            # 提取父类别标签（根据需求，我们只看父类别）
            for child_label, parent_label in parent_cls_dict.items():
                paper_labels[meta_type].add(parent_label)
                
        return paper_labels
    
    def build_graph(self, papers_data: List[Dict]):
        """
        构建元信息共现图
        
        Args:
            papers_data: 论文数据列表
        """
        print("开始构建元信息共现图...")
        
        # 统计所有标签和它们的元信息类型
        all_labels = set()
        label_to_meta_type = {}  # 标签到元信息类型的映射

        all_metatype_labels = set()
        
        # 第一遍扫描：收集所有标签信息
        for paper in papers_data:
            paper_labels = self.extract_labels_from_paper(paper)
            
            for meta_type, labels in paper_labels.items():
                for label in labels:
                    # all_labels.add(label) # 不同 meta type 可能会重复 label
                    all_metatype_labels.add((meta_type, label))
                    # if label not in label_to_meta_type:
                    #     label_to_meta_type[label] = meta_type
                    self.meta_type_labels[meta_type].add(label)
        
        # 添加所有节点到图中
        for metatype_label in all_metatype_labels:
            self.graph.add_node(metatype_label[0] + "-" + metatype_label[1])
            self.node_info[metatype_label[0] + "-" + metatype_label[1]] = {
                'id': metatype_label,
                'meta_info_type': metatype_label[0],
                'label_name': metatype_label[0] + "-" + metatype_label[1],
                'co_occurrence_count': 0  # 共现次数，后续计算
            }

        # for label in all_labels:
        #     meta_type = label_to_meta_type[label]
        #     self.graph.add_node(label)
        #     self.node_info[label] = {
        #         'id': label,
        #         'meta_info_type': meta_type,
        #         'label_name': label,
        #         'co_occurrence_count': 0  # 共现次数，后续计算
        #     }
        
        print(f"添加了 {len(all_metatype_labels)} 个节点")
        
        # 第二遍扫描：计算共现关系和边权重
        edge_counter = defaultdict(int)

        for paper in papers_data:
            paper_labels = self.extract_labels_from_paper(paper)

            # 获取这篇论文中所有不同元信息类型的标签
            all_paper_labels = []
            for meta_type, labels in paper_labels.items():
                for label in labels:
                    all_paper_labels.append((label, meta_type))

            # 计算不同元信息类型之间的标签共现
            for i, (label1, type1) in enumerate(all_paper_labels):
                for j, (label2, type2) in enumerate(all_paper_labels):
                    if i < j and type1 != type2:  # 确保不同类型，可以相同标签
                        # 确保边的一致性（较小的标签在前）
                        edge = tuple(sorted([type1 + "-" + label1, type2 + "-" + label2]))
                        edge_counter[edge] += 1
        
        # 添加边到图中
        for (a, b), weight in edge_counter.items():
            self.graph.add_edge(a, b, weight=weight)
            self.edge_weights[(a, b)] = weight
        
        print(f"添加了 {len(edge_counter)} 条边")
        
        # 更新节点的共现统计
        for node in self.graph.nodes():
            print(node)
            # 计算该节点的总共现次数（所有相邻边的权重之和）
            total_co_occurrence = sum(
                self.graph[node][neighbor]['weight'] 
                for neighbor in self.graph.neighbors(node)
            )
            self.node_info[node]['co_occurrence_count'] = total_co_occurrence
    
    def get_graph_statistics(self) -> Dict:
        """
        获取图的统计信息
        
        Returns:
            图统计信息字典
        """
        stats = {
            'total_nodes': self.graph.number_of_nodes(),
            'total_edges': self.graph.number_of_edges(),
            'meta_types_count': len(self.meta_type_labels),
            'meta_types': list(self.meta_type_labels.keys()),
            'average_degree': sum(dict(self.graph.degree()).values()) / self.graph.number_of_nodes() if self.graph.number_of_nodes() > 0 else 0,
            'max_edge_weight': max([data['weight'] for _, _, data in self.graph.edges(data=True)]) if self.graph.number_of_edges() > 0 else 0,
            'connected_components': nx.number_connected_components(self.graph)
        }
        
        # 按元信息类型统计节点数量
        type_node_counts = {}
        for meta_type, labels in self.meta_type_labels.items():
            type_node_counts[meta_type] = len(labels)
        stats['nodes_by_meta_type'] = type_node_counts
        
        return stats
    
    def get_top_nodes_by_degree(self, top_k: int = 10) -> List[Tuple[str, int]]:
        """
        获取度数最高的前K个节点
        
        Args:
            top_k: 返回前K个节点
            
        Returns:
            (节点名称, 度数) 的列表
        """
        degree_dict = dict(self.graph.degree())
        sorted_nodes = sorted(degree_dict.items(), key=lambda x: x[1], reverse=True)
        return sorted_nodes[:top_k]
    
    def get_top_edges_by_weight(self, top_k: int = 10) -> List[Tuple[str, str, int]]:
        """
        获取权重最高的前K条边
        
        Args:
            top_k: 返回前K条边
            
        Returns:
            (节点1, 节点2, 权重) 的列表
        """
        edges_with_weights = [
            (u, v, data['weight']) 
            for u, v, data in self.graph.edges(data=True)
        ]
        sorted_edges = sorted(edges_with_weights, key=lambda x: x[2], reverse=True)
        return sorted_edges[:top_k]
    
    def save_graph_data(self, output_path: str):
        """
        保存图数据到JSON文件
        
        Args:
            output_path: 输出文件路径
        """
        graph_data = {
            'nodes': [],
            'edges': [],
            'statistics': self.get_graph_statistics()
        }
        
        # 保存节点信息
        for node in self.graph.nodes():
            node_data = self.node_info[node].copy()
            node_data['degree'] = self.graph.degree(node)
            graph_data['nodes'].append(node_data)
        
        # 保存边信息
        for u, v, data in self.graph.edges(data=True):
            edge_data = {
                'source': u,
                'target': v,
                'weight': data['weight'],
                'source_meta_type': self.node_info[u]['meta_info_type'],
                'target_meta_type': self.node_info[v]['meta_info_type']
            }
            graph_data['edges'].append(edge_data)
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(graph_data, f, ensure_ascii=False, indent=2)
            print(f"图数据已保存到: {output_path}")
        except Exception as e:
            print(f"保存图数据失败: {e}")
    
    def print_analysis_report(self):
        """打印分析报告"""
        print("\n" + "="*60)
        print("元信息共现图分析报告")
        print("="*60)
        
        stats = self.get_graph_statistics()
        
        print(f"\n基本统计:")
        print(f"  总节点数: {stats['total_nodes']}")
        print(f"  总边数: {stats['total_edges']}")
        print(f"  元信息类型数: {stats['meta_types_count']}")
        print(f"  平均度数: {stats['average_degree']:.2f}")
        print(f"  最大边权重: {stats['max_edge_weight']}")
        print(f"  连通分量数: {stats['connected_components']}")
        
        print(f"\n各元信息类型的节点数量:")
        for meta_type, count in stats['nodes_by_meta_type'].items():
            print(f"  {meta_type}: {count} 个节点")
        
        print(f"\n度数最高的前10个节点:")
        top_nodes = self.get_top_nodes_by_degree(10)
        for i, (node, degree) in enumerate(top_nodes, 1):
            meta_type = self.node_info[node]['meta_info_type']
            print(f"  {i:2d}. {node} (度数: {degree}, 类型: {meta_type})")
        
        print(f"\n权重最高的前10条边:")
        top_edges = self.get_top_edges_by_weight(10)
        for i, (u, v, weight) in enumerate(top_edges, 1):
            u_type = self.node_info[u]['meta_info_type']
            v_type = self.node_info[v]['meta_info_type']
            print(f"  {i:2d}. {u} -- {v} (权重: {weight})")
            print(f"      类型: {u_type} -- {v_type}")


def main():
    """主函数"""
    # 创建图构建器
    builder = MetaInfoGraphBuilder()
    
    # 加载数据
    papers_data = builder.load_data('small_meta_list.json')
    
    if not papers_data:
        print("没有加载到数据，程序退出")
        return
    
    # 构建图
    builder.build_graph(papers_data)
    
    # 打印分析报告
    builder.print_analysis_report()
    
    # 保存图数据
    builder.save_graph_data('meta_info_graph.json')
    
    print(f"\n图构建完成！")
    print(f"- 图对象可通过 builder.graph 访问")
    print(f"- 节点信息可通过 builder.node_info 访问")
    print(f"- 图数据已保存到 meta_info_graph.json")


if __name__ == "__main__":
    main()
