#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
元信息图可视化工具
提供多种方式可视化元信息共现图
"""

import json
import networkx as nx
import matplotlib.pyplot as plt
import matplotlib
import numpy as np
from collections import defaultdict
import seaborn as sns

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False


class GraphVisualizer:
    """图可视化器"""
    
    def __init__(self, graph_data_path: str):
        """
        初始化可视化器
        
        Args:
            graph_data_path: 图数据JSON文件路径
        """
        self.graph_data = self.load_graph_data(graph_data_path)
        self.graph = self.build_networkx_graph()
        
    def load_graph_data(self, file_path: str) -> dict:
        """加载图数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"成功加载图数据: {len(data['nodes'])} 个节点, {len(data['edges'])} 条边")
            return data
        except Exception as e:
            print(f"加载图数据失败: {e}")
            return {}
    
    def build_networkx_graph(self) -> nx.Graph:
        """构建NetworkX图对象"""
        G = nx.Graph()
        
        # 添加节点
        for node in self.graph_data['nodes']:
            G.add_node(node['id'], **node)
        
        # 添加边
        for edge in self.graph_data['edges']:
            G.add_edge(edge['source'], edge['target'], weight=edge['weight'])
        
        return G
    
    def visualize_full_graph(self, figsize=(20, 16), min_weight=5):
        """
        可视化完整图（过滤低权重边）
        
        Args:
            figsize: 图片大小
            min_weight: 最小边权重阈值
        """
        # 过滤低权重边
        filtered_edges = [(u, v) for u, v, d in self.graph.edges(data=True) 
                         if d['weight'] >= min_weight]
        
        # 创建子图
        subgraph = self.graph.edge_subgraph(filtered_edges)
        
        plt.figure(figsize=figsize)
        
        # 设置布局
        pos = nx.spring_layout(subgraph, k=3, iterations=50)
        
        # 按元信息类型给节点着色
        meta_types = list(set(nx.get_node_attributes(subgraph, 'meta_info_type').values()))
        colors = plt.cm.Set3(np.linspace(0, 1, len(meta_types)))
        color_map = dict(zip(meta_types, colors))
        
        node_colors = [color_map[subgraph.nodes[node]['meta_info_type']] for node in subgraph.nodes()]
        
        # 根据度数设置节点大小
        node_sizes = [subgraph.degree(node) * 20 for node in subgraph.nodes()]
        
        # 绘制节点
        nx.draw_networkx_nodes(subgraph, pos, node_color=node_colors, 
                              node_size=node_sizes, alpha=0.8)
        
        # 绘制边，根据权重设置粗细
        edge_weights = [subgraph[u][v]['weight'] for u, v in subgraph.edges()]
        nx.draw_networkx_edges(subgraph, pos, width=[w/10 for w in edge_weights], 
                              alpha=0.6, edge_color='gray')
        
        # 添加标签（只显示度数最高的节点）
        top_nodes = sorted(subgraph.degree(), key=lambda x: x[1], reverse=True)[:20]
        top_node_labels = {node: node for node, _ in top_nodes}
        nx.draw_networkx_labels(subgraph, pos, labels=top_node_labels, 
                               font_size=8, font_weight='bold')
        
        plt.title(f'元信息共现图 (边权重 >= {min_weight})', fontsize=16, fontweight='bold')
        plt.axis('off')
        
        # 添加图例
        legend_elements = [plt.Line2D([0], [0], marker='o', color='w', 
                                     markerfacecolor=color_map[mt], markersize=10, label=mt)
                          for mt in meta_types]
        plt.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(1, 1))
        
        plt.tight_layout()
        plt.savefig('output/meta_info_graph_full.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def visualize_top_connections(self, top_k=50, figsize=(16, 12)):
        """
        可视化权重最高的前K条边及其相关节点
        
        Args:
            top_k: 显示前K条边
            figsize: 图片大小
        """
        # 获取权重最高的边
        edges_with_weights = [(u, v, d['weight']) for u, v, d in self.graph.edges(data=True)]
        top_edges = sorted(edges_with_weights, key=lambda x: x[2], reverse=True)[:top_k]
        
        # 创建子图
        edge_list = [(u, v) for u, v, _ in top_edges]
        subgraph = self.graph.edge_subgraph(edge_list)
        
        plt.figure(figsize=figsize)
        
        # 设置布局
        pos = nx.spring_layout(subgraph, k=2, iterations=50)
        
        # 按元信息类型给节点着色
        meta_types = list(set(nx.get_node_attributes(subgraph, 'meta_info_type').values()))
        colors = plt.cm.tab20(np.linspace(0, 1, len(meta_types)))
        color_map = dict(zip(meta_types, colors))
        
        node_colors = [color_map[subgraph.nodes[node]['meta_info_type']] for node in subgraph.nodes()]
        
        # 根据度数设置节点大小
        node_sizes = [subgraph.degree(node) * 50 for node in subgraph.nodes()]
        
        # 绘制节点
        nx.draw_networkx_nodes(subgraph, pos, node_color=node_colors, 
                              node_size=node_sizes, alpha=0.8)
        
        # 绘制边，根据权重设置粗细和颜色
        edge_weights = [subgraph[u][v]['weight'] for u, v in subgraph.edges()]
        max_weight = max(edge_weights)
        edge_widths = [w/max_weight * 8 for w in edge_weights]
        
        nx.draw_networkx_edges(subgraph, pos, width=edge_widths, 
                              alpha=0.7, edge_color='darkblue')
        
        # 添加所有节点标签
        nx.draw_networkx_labels(subgraph, pos, font_size=9, font_weight='bold')
        
        plt.title(f'权重最高的前{top_k}条边', fontsize=16, fontweight='bold')
        plt.axis('off')
        
        # 添加图例
        legend_elements = [plt.Line2D([0], [0], marker='o', color='w', 
                                     markerfacecolor=color_map[mt], markersize=10, label=mt)
                          for mt in meta_types]
        plt.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(1, 1))
        
        plt.tight_layout()
        plt.savefig('output/meta_info_graph_top_connections.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_meta_type_distribution(self, figsize=(12, 8)):
        """绘制元信息类型分布图"""
        stats = self.graph_data['statistics']
        meta_type_counts = stats['nodes_by_meta_type']
        
        plt.figure(figsize=figsize)
        
        # 按节点数量排序
        sorted_types = sorted(meta_type_counts.items(), key=lambda x: x[1], reverse=True)
        types, counts = zip(*sorted_types)
        
        # 创建条形图
        bars = plt.bar(range(len(types)), counts, color=plt.cm.Set3(np.linspace(0, 1, len(types))))
        
        # 设置标签
        plt.xlabel('元信息类型', fontsize=12, fontweight='bold')
        plt.ylabel('节点数量', fontsize=12, fontweight='bold')
        plt.title('各元信息类型的节点数量分布', fontsize=14, fontweight='bold')
        
        # 设置x轴标签
        plt.xticks(range(len(types)), types, rotation=45, ha='right')
        
        # 在条形图上添加数值
        for i, bar in enumerate(bars):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{int(height)}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig('output/meta_type_distribution.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_degree_distribution(self, figsize=(10, 6)):
        """绘制度数分布图"""
        degrees = [d for n, d in self.graph.degree()]
        
        plt.figure(figsize=figsize)
        
        # 绘制直方图
        plt.hist(degrees, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        
        plt.xlabel('节点度数', fontsize=12, fontweight='bold')
        plt.ylabel('节点数量', fontsize=12, fontweight='bold')
        plt.title('节点度数分布', fontsize=14, fontweight='bold')
        
        # 添加统计信息
        mean_degree = np.mean(degrees)
        plt.axvline(mean_degree, color='red', linestyle='--', 
                   label=f'平均度数: {mean_degree:.2f}')
        plt.legend()
        
        plt.tight_layout()
        plt.savefig('output/degree_distribution.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def analyze_meta_type_connections(self):
        """分析不同元信息类型之间的连接模式"""
        # 统计不同元信息类型之间的连接
        type_connections = defaultdict(int)
        
        for edge in self.graph_data['edges']:
            source_type = edge['source_meta_type']
            target_type = edge['target_meta_type']
            
            # 确保类型对的一致性
            type_pair = tuple(sorted([source_type, target_type]))
            type_connections[type_pair] += edge['weight']
        
        # 创建连接矩阵
        meta_types = list(self.graph_data['statistics']['nodes_by_meta_type'].keys())
        n_types = len(meta_types)
        connection_matrix = np.zeros((n_types, n_types))
        
        for i, type1 in enumerate(meta_types):
            for j, type2 in enumerate(meta_types):
                if i <= j:
                    type_pair = tuple(sorted([type1, type2]))
                    weight = type_connections.get(type_pair, 0)
                    connection_matrix[i, j] = weight
                    connection_matrix[j, i] = weight
        
        # 绘制热力图
        plt.figure(figsize=(14, 12))
        
        # 使用对数尺度以更好地显示差异
        log_matrix = np.log1p(connection_matrix)
        
        sns.heatmap(log_matrix, annot=True, fmt='.1f', 
                   xticklabels=meta_types, yticklabels=meta_types,
                   cmap='YlOrRd', square=True)
        
        plt.title('元信息类型间连接强度热力图 (对数尺度)', fontsize=14, fontweight='bold')
        plt.xlabel('元信息类型', fontsize=12, fontweight='bold')
        plt.ylabel('元信息类型', fontsize=12, fontweight='bold')
        
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        
        plt.tight_layout()
        plt.savefig('output/meta_type_connections_heatmap.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return type_connections


def main():
    """主函数"""
    # 创建可视化器
    visualizer = GraphVisualizer('meta_info_graph.json')
    
    print("开始生成可视化图表...")
    
    # 1. 元信息类型分布
    print("1. 生成元信息类型分布图...")
    visualizer.plot_meta_type_distribution()
    
    # 2. 度数分布
    print("2. 生成度数分布图...")
    visualizer.plot_degree_distribution()
    
    # 3. 类型间连接热力图
    print("3. 生成类型间连接热力图...")
    visualizer.analyze_meta_type_connections()
    
    # 4. 权重最高的连接
    print("4. 生成权重最高的连接图...")
    visualizer.visualize_top_connections(top_k=30)
    
    # 5. 完整图（过滤低权重边）
    print("5. 生成完整图...")
    visualizer.visualize_full_graph(min_weight=10)
    
    print("所有可视化图表生成完成！")


if __name__ == "__main__":
    main()
