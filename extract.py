import json

small_meta_list = []

with open(r"C:\CodingPlace\Learning\edu\master_paper\metadata\data\meta_classified_papers.jsonl", "r", encoding="utf-8") as f:
    for line in f:
        record = json.loads(line)
        small_meta_list.append({
            "id": record["id"],
            "cleaned_meta_info": record["cleaned_meta_info"]
        })

with open("small_meta_list.json", "w", encoding="utf-8") as f:
    json.dump(small_meta_list, f, ensure_ascii=False, indent=2)